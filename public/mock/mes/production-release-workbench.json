{"workOrderDetails": {"WO-2024-001": {"id": "WO-2024-001", "workOrderNumber": "WO-2024-001", "customerName": "华润置地", "customerOrderId": "CO-2024-001", "customerOrderNumber": "华润-幕墙-240101", "items": [{"id": "WOI-001", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-001", "customerOrderNumber": "华润-幕墙-240101", "customerName": "华润置地", "projectName": "华润中心A座幕墙工程", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e"}, "quantity": 450, "processFlow": [{"stepName": "切割", "workstation": "切割台#1", "estimatedDuration": 180, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#1", "estimatedDuration": 90, "status": "pending", "constraints": {}}, {"stepName": "镀膜", "workstation": "镀膜线#1", "estimatedDuration": 240, "status": "pending", "constraints": {}}, {"stepName": "钢化", "workstation": "钢化炉#1", "estimatedDuration": 300, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#1", "estimatedDuration": 45, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-08-30", "priority": "high"}, {"id": "WOI-002", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-002", "customerOrderNumber": "华润-幕墙-240101", "customerName": "华润置地", "projectName": "华润中心A座幕墙工程", "specifications": {"length": 2400, "width": 1600, "thickness": 8, "glassType": "low_e"}, "quantity": 280, "processFlow": [{"stepName": "切割", "workstation": "切割台#2", "estimatedDuration": 140, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#2", "estimatedDuration": 70, "status": "pending", "constraints": {}}, {"stepName": "镀膜", "workstation": "镀膜线#1", "estimatedDuration": 200, "status": "pending", "constraints": {}}, {"stepName": "钢化", "workstation": "钢化炉#2", "estimatedDuration": 240, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#2", "estimatedDuration": 35, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-08-30", "priority": "high"}, {"id": "WOI-003", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-003", "customerOrderNumber": "华润-幕墙-240101", "customerName": "华润置地", "projectName": "华润中心A座幕墙工程", "specifications": {"length": 1500, "width": 1000, "thickness": 5, "glassType": "clear"}, "quantity": 180, "processFlow": [{"stepName": "切割", "workstation": "切割台#1", "estimatedDuration": 90, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#1", "estimatedDuration": 45, "status": "pending", "constraints": {}}, {"stepName": "钢化", "workstation": "钢化炉#1", "estimatedDuration": 150, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#1", "estimatedDuration": 25, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-08-30", "priority": "high"}]}, "WO-2024-005": {"id": "WO-2024-005", "workOrderNumber": "WO-2024-005", "customerName": "中海地产", "customerOrderId": "CO-2024-004", "customerOrderNumber": "中海-中空-240104", "items": [{"id": "WOI-016", "productionOrderId": "WO-2024-005", "customerOrderItemId": "COI-008", "customerOrderNumber": "中海-中空-240104", "customerName": "中海地产", "projectName": "中海地产中空玻璃项目", "specifications": {"length": 1400, "width": 800, "thickness": 5, "glassType": "low_e", "color": "透明", "structure": "5+12A+5", "gasType": "氩气", "sealant": "双道密封"}, "quantity": 150, "processFlow": [{"stepName": "切割", "workstation": "切割台#2", "estimatedDuration": 120, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#2", "estimatedDuration": 80, "status": "pending", "constraints": {}}, {"stepName": "镀膜", "workstation": "镀膜线#1", "estimatedDuration": 180, "status": "pending", "constraints": {}}, {"stepName": "钢化", "workstation": "钢化炉#2", "estimatedDuration": 200, "status": "pending", "constraints": {}}, {"stepName": "合片", "workstation": "中空线#1", "estimatedDuration": 150, "status": "pending", "constraints": {}}, {"stepName": "充气", "workstation": "中空线#1", "estimatedDuration": 60, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#2", "estimatedDuration": 45, "status": "pending", "constraints": {}}, {"stepName": "包装", "workstation": "包装台#1", "estimatedDuration": 30, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-08-30", "priority": "high"}]}, "WO-2024-002": {"id": "WO-2024-002", "workOrderNumber": "WO-2024-002", "customerName": "万科集团", "customerOrderId": "CO-2024-0156", "customerOrderNumber": "CO-2024-0156", "items": [{"id": "WOI-001", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-001", "customerOrderNumber": "CO-2024-0156", "customerName": "万科集团", "projectName": "万科翡翠公园A栋", "specifications": {"length": 1200, "width": 800, "thickness": 5, "glassType": "clear"}, "quantity": 180, "processFlow": [{"stepName": "切割", "workstation": "切割台#1", "estimatedDuration": 120, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#2", "estimatedDuration": 60, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#1", "estimatedDuration": 30, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-09-04", "priority": "medium"}, {"id": "WOI-002", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-002", "customerOrderNumber": "CO-2024-0156", "customerName": "万科集团", "projectName": "万科翡翠公园A栋", "specifications": {"length": 1500, "width": 1200, "thickness": 5, "glassType": "clear"}, "quantity": 120, "processFlow": [{"stepName": "切割", "workstation": "切割台#1", "estimatedDuration": 90, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#2", "estimatedDuration": 45, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#1", "estimatedDuration": 20, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-09-04", "priority": "medium"}, {"id": "WOI-003", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-003", "customerOrderNumber": "CO-2024-0157", "customerName": "保利地产", "projectName": "保利天汇B栋", "specifications": {"length": 2000, "width": 1400, "thickness": 8, "glassType": "tinted"}, "quantity": 85, "processFlow": [{"stepName": "切割", "workstation": "切割台#2", "estimatedDuration": 150, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#1", "estimatedDuration": 75, "status": "pending", "constraints": {}}, {"stepName": "钢化", "workstation": "钢化炉#1", "estimatedDuration": 180, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#2", "estimatedDuration": 40, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-09-06", "priority": "high"}]}, "WO-2024-003": {"id": "WO-2024-003", "workOrderNumber": "WO-2024-003", "customerName": "保利地产", "customerOrderId": "CO-2024-0158", "customerOrderNumber": "CO-2024-0158", "items": [{"id": "WOI-004", "productionOrderId": "WO-2024-003", "customerOrderItemId": "COI-004", "customerOrderNumber": "CO-2024-0158", "customerName": "保利地产", "projectName": "保利天汇C栋", "specifications": {"length": 1200, "width": 800, "thickness": 5, "glassType": "clear"}, "quantity": 220, "processFlow": [{"stepName": "切割", "workstation": "切割台#1", "estimatedDuration": 160, "status": "pending", "constraints": {}}, {"stepName": "磨边", "workstation": "磨边机#2", "estimatedDuration": 80, "status": "pending", "constraints": {}}, {"stepName": "质检", "workstation": "质检台#1", "estimatedDuration": 35, "status": "pending", "constraints": {}}], "currentStatus": "pending", "utilizationRate": 0.0, "deliveryDate": "2025-09-05", "priority": "medium"}]}}, "technicalSpecifications": {"WO-2024-001": {"glassSpecs": {"status": "confirmed", "details": {"thickness": ["5mm", "6mm", "8mm"], "types": ["透明玻璃", "Low-E玻璃"], "sizes": "符合标准原片切割", "standards": "GB/T 11614-2009"}}, "processRequirements": {"status": "confirmed", "details": {"cutting": "精密切割", "edging": "直边磨边", "surfaceTreatment": "Low-E镀膜", "tolerance": "±0.5mm"}}, "qualityStandards": {"status": "confirmed", "details": {"flatness": "≤0.3mm/m", "edgeQuality": "无崩边、无裂纹", "specialRequirements": "幕墙专用钢化玻璃", "inspectionStandard": "GB/T 9963-1998"}}, "packagingRequirements": {"status": "confirmed", "details": {"method": "木箱包装", "protection": "防震泡沫", "labeling": "项目标识+规格标签", "stackingLimit": "最多5层"}}}, "WO-2024-002": {"glassSpecs": {"status": "confirmed", "details": {"thickness": ["5mm", "8mm"], "types": ["透明玻璃", "有色玻璃"], "sizes": "符合标准原片切割", "standards": "GB/T 11614-2009"}}, "processRequirements": {"status": "confirmed", "details": {"cutting": "精密切割", "edging": "直边磨边", "surfaceTreatment": "标准清洁", "tolerance": "±0.5mm"}}, "qualityStandards": {"status": "pending", "details": {"flatness": "±0.3mm", "edgeQuality": "A级", "specialRequirements": "需确认特殊要求", "inspectionStandard": "JC/T 2129-2012"}}, "packagingRequirements": {"status": "confirmed", "details": {"method": "木箱包装", "protection": "泡沫垫", "labeling": "标准标识", "stackingLimit": "最多5层"}}}, "WO-2024-005": {"glassSpecs": {"status": "confirmed", "details": {"thickness": ["5mm"], "types": ["Low-E玻璃"], "structure": "5+12A+5中空结构", "gasType": "氩气充填", "sealant": "双道密封胶", "standards": "GB/T 11944-2012"}}, "processRequirements": {"status": "confirmed", "details": {"cutting": "精密切割", "edging": "直边磨边", "coating": "Low-E镀膜", "tempering": "钢化处理", "assembly": "中空合片", "gasInjection": "氩气充填", "tolerance": "±0.5mm"}}, "qualityStandards": {"status": "confirmed", "details": {"flatness": "≤0.3mm/m", "edgeQuality": "无崩边、无裂纹", "sealingQuality": "密封性能A级", "gasContent": "氩气含量≥85%", "inspectionStandard": "GB/T 11944-2012"}}, "packagingRequirements": {"status": "confirmed", "details": {"method": "专用中空玻璃包装架", "protection": "防震垫片+保护膜", "labeling": "项目标识+规格标签+方向标识", "stackingLimit": "最多3层"}}}}, "bomValidation": {"WO-2024-001": {"items": [{"id": "BOM-001", "materialType": "原片玻璃", "materialName": "6mm Low-E玻璃", "specification": "3300x2140x6mm", "requiredQuantity": 15, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "信义玻璃"}, {"id": "BOM-002", "materialType": "原片玻璃", "materialName": "8mm Low-E玻璃", "specification": "3300x2140x8mm", "requiredQuantity": 8, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "信义玻璃"}, {"id": "BOM-003", "materialType": "原片玻璃", "materialName": "5mm透明玻璃", "specification": "3300x2140x5mm", "requiredQuantity": 5, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "南玻集团"}, {"id": "BOM-004", "materialType": "辅助材料", "materialName": "切割油", "specification": "工业级", "requiredQuantity": 2, "unit": "桶", "status": "sufficient", "stockStatus": "库存充足", "supplier": "本地供应商"}, {"id": "BOM-005", "materialType": "包装材料", "materialName": "木箱包装", "specification": "定制尺寸", "requiredQuantity": 25, "unit": "套", "status": "pending", "stockStatus": "需要采购", "supplier": "包装厂"}], "summary": {"totalItems": 5, "sufficientItems": 4, "pendingItems": 1, "completeness": 95}}, "WO-2024-002": {"items": [{"id": "BOM-001", "materialType": "原片玻璃", "materialName": "5mm透明玻璃原片", "specification": "3300x2140", "requiredQuantity": 2, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "信义玻璃"}, {"id": "BOM-002", "materialType": "原片玻璃", "materialName": "8mm有色玻璃原片", "specification": "3660x2440", "requiredQuantity": 1, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "福耀玻璃"}, {"id": "BOM-003", "materialType": "辅助材料", "materialName": "包装材料、标识标签", "specification": "标准规格", "requiredQuantity": 3, "unit": "套", "status": "pending", "stockStatus": "待确认", "supplier": "包装供应商"}], "summary": {"totalItems": 3, "sufficientItems": 2, "pendingItems": 1, "completeness": 95}}, "WO-2024-005": {"items": [{"id": "BOM-IGU-001", "materialType": "原片玻璃", "materialName": "5mm Low-E玻璃", "specification": "3300x2140x5mm", "requiredQuantity": 8, "unit": "片", "status": "sufficient", "stockStatus": "库存充足", "supplier": "信义玻璃"}, {"id": "BOM-IGU-002", "materialType": "间隔条", "materialName": "铝间隔条", "specification": "12mm宽度", "requiredQuantity": 320, "unit": "米", "status": "sufficient", "stockStatus": "库存充足", "supplier": "间隔条厂"}, {"id": "BOM-IGU-003", "materialType": "密封胶", "materialName": "双组份结构胶", "specification": "中空玻璃专用", "requiredQuantity": 15, "unit": "支", "status": "sufficient", "stockStatus": "库存充足", "supplier": "道康宁"}, {"id": "BOM-IGU-004", "materialType": "干燥剂", "materialName": "分子筛干燥剂", "specification": "3A型", "requiredQuantity": 5, "unit": "公斤", "status": "sufficient", "stockStatus": "库存充足", "supplier": "干燥剂厂"}, {"id": "BOM-IGU-005", "materialType": "充气材料", "materialName": "氩气", "specification": "99.9%纯度", "requiredQuantity": 2, "unit": "瓶", "status": "sufficient", "stockStatus": "库存充足", "supplier": "气体公司"}, {"id": "BOM-IGU-006", "materialType": "包装材料", "materialName": "中空玻璃包装架", "specification": "定制尺寸", "requiredQuantity": 8, "unit": "套", "status": "sufficient", "stockStatus": "库存充足", "supplier": "包装厂"}], "summary": {"totalItems": 6, "sufficientItems": 6, "pendingItems": 0, "completeness": 100}}}, "materialStock": {"raw_glass_5mm_clear": {"materialCode": "RG-5MM-CLR", "materialName": "5mm透明玻璃原片", "specification": "3300x2140", "currentStock": 8, "unit": "片", "unitCost": 280.0, "supplier": "信义玻璃", "leadTime": 3}, "raw_glass_8mm_tinted": {"materialCode": "RG-8MM-TNT", "materialName": "8mm有色玻璃原片", "specification": "3660x2440", "currentStock": 5, "unit": "片", "unitCost": 450.0, "supplier": "福耀玻璃", "leadTime": 5}, "packaging_material": {"materialCode": "PKG-STD", "materialName": "标准包装材料", "specification": "木箱+泡沫垫", "currentStock": 2, "unit": "套", "unitCost": 35.0, "supplier": "包装供应商", "leadTime": 1}, "special_labels": {"materialCode": "LBL-SPC", "materialName": "特殊标识标签", "specification": "防水标签", "currentStock": 0, "unit": "张", "unitCost": 2.5, "supplier": "标签供应商", "leadTime": 2}}, "workstationCapacity": {"cutting": {"name": "切割工段", "currentLoad": 75, "maxCapacity": 100, "availableCapacity": 25, "status": "available"}, "edging": {"name": "磨边工段", "currentLoad": 60, "maxCapacity": 100, "availableCapacity": 40, "status": "available"}, "packaging": {"name": "包装工段", "currentLoad": 90, "maxCapacity": 100, "availableCapacity": 10, "status": "tight"}, "tempering": {"name": "钢化工段", "currentLoad": 85, "maxCapacity": 100, "availableCapacity": 15, "status": "available"}}, "optimizationSuggestions": {"WO-2024-002": {"canMergeWith": ["WO-2024-003"], "mergeRecommendation": {"type": "cutting_optimization", "description": "发现其他1个待发布工单中的1个订单项可与当前工单合并进行切割，预计可提升原片利用率7%", "benefits": {"efficiencyImprovement": 15, "timeSaving": 2, "costReduction": 8, "materialUtilization": 92}, "mergeDetails": {"targetWorkOrder": "WO-2024-003", "targetCustomer": "保利地产", "targetItems": 1, "combinedQuantity": 605, "estimatedDuration": 3, "materialUtilization": 92}}}, "WO-2024-005": {"canMergeWith": [], "mergeRecommendation": {"type": "process_optimization", "description": "中空玻璃工艺流程优化建议：建议调整钢化和合片工序的排程，可减少中间存储时间15%", "benefits": {"efficiencyImprovement": 12, "timeSaving": 1.5, "costReduction": 5, "qualityImprovement": 8}, "processDetails": {"optimizedFlow": "切割→磨边→镀膜→钢化→合片→充气→质检→包装", "keyImprovement": "钢化后立即进入合片工序", "estimatedDuration": 4.2, "qualityBenefit": "减少玻璃表面污染风险"}}, "specialConsiderations": {"gasInjection": "氩气充填需要专用设备，确保充填纯度≥85%", "sealingQuality": "双道密封胶施工需要严格控制环境湿度", "qualityControl": "中空玻璃需要额外的密封性能检测", "packaging": "使用专用包装架，避免玻璃变形"}}}}