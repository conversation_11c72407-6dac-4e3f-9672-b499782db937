{"scenario": "时间优化的生产工单数据", "description": "基于2025年8月19日的时间背景，优化了所有日期字段以符合实际业务场景", "lastUpdated": "2025-08-19T09:00:00.000Z", "relationshipTypes": {"oneToOne": "单客户单订单工单", "oneToMany": "单客户多订单工单", "manyToOne": "多客户单工单（批量生产）", "manyToMany": "多客户多订单复合工单"}, "productionOrders": [{"id": "WO-2024-001", "workOrderNumber": "WO-2024-001", "customerOrderId": "CO-2024-001", "customerOrderNumber": "华润-幕墙-240101", "customerId": "CUST-001", "customerName": "华润置地", "orderType": "幕墙工程", "priority": "high", "status": "pending", "plannedStartDate": "2025-08-21T00:00:00.000Z", "plannedEndDate": "2025-08-27T10:00:00.000Z", "createdAt": "2025-08-17T08:27:00.000Z", "items": [{"id": "WOI-001", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-001", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1800, "width": 1200, "thickness": 6, "glassType": "low_e", "color": "透明"}, "quantity": 450, "unitPrice": 280, "totalAmount": 126000, "deliveryDate": "2025-08-30T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-17T08:27:00.000Z", "customerName": "华润置地", "customerOrderNumber": "华润-幕墙-240101"}, {"id": "WOI-002", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-002", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 2400, "width": 1600, "thickness": 6, "glassType": "low_e", "color": "透明"}, "quantity": 280, "unitPrice": 320, "totalAmount": 89600, "deliveryDate": "2025-08-30T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-17T08:27:00.000Z", "customerName": "华润置地", "customerOrderNumber": "华润-幕墙-240101"}, {"id": "WOI-003", "productionOrderId": "WO-2024-001", "customerOrderItemId": "COI-003", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1500, "width": 1000, "thickness": 6, "glassType": "low_e", "color": "透明"}, "quantity": 320, "unitPrice": 250, "totalAmount": 80000, "deliveryDate": "2025-08-29T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-17T08:27:00.000Z", "customerName": "华润置地", "customerOrderNumber": "华润-幕墙-240101"}]}, {"id": "WO-2024-002", "workOrderNumber": "WO-2024-002", "customerOrderId": "CO-2024-002", "customerOrderNumber": "万科-门窗-240102", "customerId": "CUST-002", "customerName": "万科集团", "orderType": "混合订单", "priority": "medium", "status": "pending", "plannedStartDate": "2025-08-20T00:00:00.000Z", "plannedEndDate": "2025-09-04T10:00:00.000Z", "createdAt": "2025-08-14T03:02:00.000Z", "items": [{"id": "WOI-004", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-004", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1200, "width": 800, "thickness": 5, "glassType": "clear", "color": "透明"}, "quantity": 180, "unitPrice": 120, "totalAmount": 21600, "deliveryDate": "2025-09-07T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-14T03:02:00.000Z", "customerName": "万科集团", "customerOrderNumber": "万科-门窗-240102"}, {"id": "WOI-005", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-005", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1500, "width": 1200, "thickness": 5, "glassType": "clear", "color": "透明"}, "quantity": 120, "unitPrice": 150, "totalAmount": 18000, "deliveryDate": "2025-09-05T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-14T03:02:00.000Z", "customerName": "万科集团", "customerOrderNumber": "万科-门窗-240102"}, {"id": "WOI-006", "productionOrderId": "WO-2024-002", "customerOrderItemId": "COI-006", "productFamilyId": "PF-DECORATIVE", "productFamilyName": "装饰玻璃产品族", "specifications": {"length": 2000, "width": 1400, "thickness": 8, "glassType": "tinted", "color": "灰色"}, "quantity": 85, "unitPrice": 380, "totalAmount": 32300, "deliveryDate": "2025-09-07T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "表面处理", "workstation": "surface_treatment"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-14T03:02:00.000Z", "customerName": "绿地控股", "customerOrderNumber": "绿地-隔断-240103"}]}, {"id": "WO-2024-003", "workOrderNumber": "WO-2024-003", "customerOrderId": "CO-2024-004", "customerOrderNumber": "批量生产工单", "customerName": "多客户批量", "orderType": "批量生产", "priority": "urgent", "status": "in_progress", "plannedStartDate": "2025-08-16T00:00:00.000Z", "plannedEndDate": "2025-10-09T10:00:00.000Z", "createdAt": "2025-08-09T05:16:00.000Z", "items": [{"id": "WOI-007", "productionOrderId": "WO-2024-003", "customerOrderItemId": "COI-007", "productFamilyId": "PF-IGU", "productFamilyName": "中空玻璃产品族", "specifications": {"length": 1600, "width": 1000, "thickness": 6, "glassType": "low_e", "color": "透明"}, "quantity": 200, "unitPrice": 450, "totalAmount": 90000, "deliveryDate": "2025-10-12T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "合片", "workstation": "insulating"}, {"stepName": "充气", "workstation": "insulating"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "in_progress", "createdAt": "2025-08-09T05:16:00.000Z", "customerName": "中海地产", "customerOrderNumber": "中海-中空-240104"}, {"id": "WOI-008", "productionOrderId": "WO-2024-003", "customerOrderItemId": "COI-009", "productFamilyId": "PF-LAMINATED", "productFamilyName": "夹胶玻璃产品族", "specifications": {"length": 2200, "width": 1500, "thickness": 10, "glassType": "clear", "color": "透明"}, "quantity": 60, "unitPrice": 680, "totalAmount": 40800, "deliveryDate": "2025-10-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "清洗", "workstation": "cleaning"}, {"stepName": "夹胶", "workstation": "laminating"}, {"stepName": "高压釜", "workstation": "autoclave"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "in_progress", "createdAt": "2025-08-09T05:16:00.000Z", "customerName": "保利地产", "customerOrderNumber": "保利-夹胶-240105"}, {"id": "WOI-009", "productionOrderId": "WO-2024-003", "customerOrderItemId": "COI-011", "productFamilyId": "PF-DECORATIVE", "productFamilyName": "装饰玻璃产品族", "specifications": {"length": 1200, "width": 600, "thickness": 6, "glassType": "reflective", "color": "蓝色"}, "quantity": 120, "unitPrice": 320, "totalAmount": 38400, "deliveryDate": "2025-10-12T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "表面处理", "workstation": "surface_treatment"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "in_progress", "createdAt": "2025-08-09T05:16:00.000Z", "customerName": "龙湖集团", "customerOrderNumber": "龙湖-装饰-240106"}]}, {"id": "WO-2024-004", "workOrderNumber": "WO-2024-004", "customerOrderId": "CO-2024-007", "customerOrderNumber": "复合生产工单", "customerName": "多客户复合", "orderType": "复合生产", "priority": "high", "status": "released", "plannedStartDate": "2025-08-18T00:00:00.000Z", "plannedEndDate": "2025-09-08T10:00:00.000Z", "createdAt": "2025-08-15T02:23:00.000Z", "items": [{"id": "WOI-010", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-013", "productFamilyId": "PF-FURNITURE", "productFamilyName": "家具玻璃产品族", "specifications": {"length": 1000, "width": 500, "thickness": 8, "glassType": "clear", "color": "透明"}, "quantity": 50, "unitPrice": 150, "totalAmount": 7500, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "抛光", "workstation": "polishing"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "融创中国", "customerOrderNumber": "融创-家具-240107"}, {"id": "WOI-011", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-015", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 2000, "width": 1000, "thickness": 8, "glassType": "low_e", "color": "透明"}, "quantity": 40, "unitPrice": 480, "totalAmount": 19200, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "碧桂园", "customerOrderNumber": "碧桂园-阳光房-240108"}, {"id": "WOI-012", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-016", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1500, "width": 2000, "thickness": 6, "glassType": "low_e", "color": "透明"}, "quantity": 30, "unitPrice": 420, "totalAmount": 12600, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "碧桂园", "customerOrderNumber": "碧桂园-阳光房-240108"}, {"id": "WOI-013", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-017", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 800, "width": 300, "thickness": 5, "glassType": "clear", "color": "透明"}, "quantity": 200, "unitPrice": 60, "totalAmount": 12000, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "恒大集团", "customerOrderNumber": "恒大-展示-240109"}, {"id": "WOI-014", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-019", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1800, "width": 1200, "thickness": 12, "glassType": "low_e", "color": "透明"}, "quantity": 25, "unitPrice": 850, "totalAmount": 21250, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "金茂集团", "customerOrderNumber": "金茂-特殊-240110"}, {"id": "WOI-015", "productionOrderId": "WO-2024-004", "customerOrderItemId": "COI-020", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 2400, "width": 1600, "thickness": 10, "glassType": "reflective", "color": "金色"}, "quantity": 35, "unitPrice": 720, "totalAmount": 25200, "deliveryDate": "2025-09-11T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-15T02:23:00.000Z", "customerName": "金茂集团", "customerOrderNumber": "金茂-特殊-240110"}]}, {"id": "WO-2024-005", "workOrderNumber": "WO-2024-005", "customerOrderId": "CO-2024-004", "customerOrderNumber": "中海-中空-240104", "customerName": "中海地产", "orderType": "中空玻璃", "priority": "high", "status": "released", "plannedStartDate": "2025-08-18T00:00:00.000Z", "plannedEndDate": "2025-08-27T10:00:00.000Z", "createdAt": "2025-08-13T02:59:00.000Z", "items": [{"id": "WOI-016", "productionOrderId": "WO-2024-005", "customerOrderItemId": "COI-008", "productFamilyId": "PF-IGU", "productFamilyName": "中空玻璃产品族", "specifications": {"length": 1400, "width": 800, "thickness": 5, "glassType": "low_e", "color": "透明"}, "quantity": 150, "unitPrice": 380, "totalAmount": 57000, "deliveryDate": "2025-08-30T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "合片", "workstation": "insulating"}, {"stepName": "充气", "workstation": "insulating"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-13T02:59:00.000Z", "customerName": "中海地产", "customerOrderNumber": "中海-中空-240104"}]}, {"id": "WO-2024-006", "workOrderNumber": "WO-2024-006", "customerOrderId": "CO-2024-005", "customerOrderNumber": "保利-夹胶-240105", "customerName": "保利地产", "orderType": "夹胶玻璃", "priority": "medium", "status": "in_progress", "plannedStartDate": "2025-08-16T00:00:00.000Z", "plannedEndDate": "2025-08-29T10:00:00.000Z", "createdAt": "2025-08-12T06:54:00.000Z", "items": [{"id": "WOI-017", "productionOrderId": "WO-2024-006", "customerOrderItemId": "COI-010", "productFamilyId": "PF-LAMINATED", "productFamilyName": "夹胶玻璃产品族", "specifications": {"length": 1800, "width": 1200, "thickness": 8, "glassType": "clear", "color": "透明"}, "quantity": 80, "unitPrice": 520, "totalAmount": 41600, "deliveryDate": "2025-08-30T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "清洗", "workstation": "cleaning"}, {"stepName": "夹胶", "workstation": "laminating"}, {"stepName": "高压釜", "workstation": "autoclave"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "in_progress", "createdAt": "2025-08-12T06:54:00.000Z", "customerName": "保利地产", "customerOrderNumber": "保利-夹胶-240105"}]}, {"id": "WO-2024-007", "workOrderNumber": "WO-2024-007", "customerOrderId": "CO-2024-006", "customerOrderNumber": "龙湖-装饰-240106", "customerName": "龙湖集团", "orderType": "装饰玻璃", "priority": "low", "status": "completed", "plannedStartDate": "2025-08-09T00:00:00.000Z", "plannedEndDate": "2025-08-15T10:00:00.000Z", "createdAt": "2025-08-08T06:57:00.000Z", "items": [{"id": "WOI-018", "productionOrderId": "WO-2024-007", "customerOrderItemId": "COI-012", "productFamilyId": "PF-DECORATIVE", "productFamilyName": "装饰玻璃产品族", "specifications": {"length": 800, "width": 600, "thickness": 5, "glassType": "tinted", "color": "茶色"}, "quantity": 100, "unitPrice": 180, "totalAmount": 18000, "deliveryDate": "2025-08-16T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "表面处理", "workstation": "surface_treatment"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "completed", "createdAt": "2025-08-08T06:57:00.000Z", "customerName": "龙湖集团", "customerOrderNumber": "龙湖-装饰-240106"}]}, {"id": "WO-2024-008", "workOrderNumber": "WO-2024-008", "customerOrderId": "CO-2024-007", "customerOrderNumber": "融创-家具-240107", "customerName": "融创中国", "orderType": "家具玻璃", "priority": "urgent", "status": "pending", "plannedStartDate": "2025-08-21T00:00:00.000Z", "plannedEndDate": "2025-08-26T10:00:00.000Z", "createdAt": "2025-08-07T02:11:00.000Z", "items": [{"id": "WOI-019", "productionOrderId": "WO-2024-008", "customerOrderItemId": "COI-014", "productFamilyId": "PF-FURNITURE", "productFamilyName": "家具玻璃产品族", "specifications": {"length": 600, "width": 400, "thickness": 5, "glassType": "clear", "color": "透明"}, "quantity": 80, "unitPrice": 80, "totalAmount": 6400, "deliveryDate": "2025-08-29T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "抛光", "workstation": "polishing"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "pending", "createdAt": "2025-08-07T02:11:00.000Z", "customerName": "融创中国", "customerOrderNumber": "融创-家具-240107"}]}, {"id": "WO-2024-009", "workOrderNumber": "WO-2024-009", "customerOrderId": "CO-2024-009", "customerOrderNumber": "恒大-展示-240109", "customerName": "恒大集团", "orderType": "展示柜", "priority": "high", "status": "released", "plannedStartDate": "2025-08-18T00:00:00.000Z", "plannedEndDate": "2025-08-20T10:00:00.000Z", "createdAt": "2025-08-14T03:48:00.000Z", "items": [{"id": "WOI-020", "productionOrderId": "WO-2024-009", "customerOrderItemId": "COI-018", "productFamilyId": "PF-TEMPERED", "productFamilyName": "单片钢化玻璃产品族", "specifications": {"length": 1000, "width": 400, "thickness": 6, "glassType": "clear", "color": "透明"}, "quantity": 100, "unitPrice": 90, "totalAmount": 9000, "deliveryDate": "2025-08-22T06:00:00.000Z", "processFlow": [{"stepName": "切割", "workstation": "cold_processing"}, {"stepName": "磨边", "workstation": "cold_processing"}, {"stepName": "镀膜", "workstation": "coating"}, {"stepName": "钢化", "workstation": "tempering"}, {"stepName": "质检", "workstation": "quality_control"}, {"stepName": "包装", "workstation": "packaging"}], "status": "released", "createdAt": "2025-08-14T03:48:00.000Z", "customerName": "恒大集团", "customerOrderNumber": "恒大-展示-240109"}]}], "timeOptimization": {"baseDate": "2025-08-19T09:00:00Z", "createdTimeRange": "最近1-14天内", "productionCycles": {"PF-TEMPERED": "3-5工作日", "PF-IGU": "7-10工作日", "PF-LAMINATED": "10-12工作日", "PF-DECORATIVE": "5-8工作日", "PF-FURNITURE": "4-6工作日"}, "priorityLogic": {"urgent": "提前2天开始", "high": "提前1天开始", "medium": "延后1天开始", "low": "延后3天开始"}, "statusLogic": {"pending": "延后2天开始", "released": "按计划开始", "in_progress": "3天前已开始", "completed": "10天前开始"}}}