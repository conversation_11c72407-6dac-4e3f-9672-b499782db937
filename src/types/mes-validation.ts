/**
 * MES制造执行系统验证相关的TypeScript接口定义
 * 用于支撑排版优化、工段调度、交期承诺三个核心验证场景
 */

import type { DeliveryPhase } from './order-delivery';

// ==================== 核心验证数据结构 ====================

/**
 * 客户订单模型（销售订单）
 */
export interface CustomerOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  projectName?: string;
  items: CustomerOrderItem[];
  requiredDate: string;
  estimatedCost: number;
  actualCost?: number;
  status: 'confirmed' | 'in_production' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  // 关联的生产工单
  productionOrders?: string[]; // 生产工单ID列表
}

/**
 * 客户订单明细
 */
export interface CustomerOrderItem {
  id: string;
  customerOrderId: string;
  specifications: {
    length: number;    // mm
    width: number;     // mm
    thickness: number; // mm
    glassType: 'clear' | 'tinted' | 'low_e' | 'reflective';
    color?: string;
  };
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  deliveryDate: string;
  notes?: string;
}

/**
 * 生产工单模型（MES内部工单）
 */
export interface ProductionOrder {
  id: string;
  workOrderNumber: string;
  customerOrderId: string; // 关联的客户订单ID
  customerOrderNumber: string; // 客户订单号（冗余字段，便于显示）
  customerName: string;
  items: ProductionOrderItem[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'released' | 'in_progress' | 'completed' | 'cancelled';
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  createdAt: string;
  updatedAt: string;
  // 关联的排版任务
  cuttingTasks?: string[]; // 排版任务ID列表

  // 交付阶段管理字段
  currentPhase?: DeliveryPhase;
  deliveryStatus?: {
    primaryStatus: 'order_confirmed' | 'planning' | 'scheduled' | 'executing' | 'delivered';
    planningSubStatus?: 'material_analysis' | 'capacity_evaluation' | 'delivery_commitment';
    schedulingSubStatus?: 'cutting_optimization' | 'resource_allocation' | 'timeline_confirmation';
    executingSubStatus?: 'production_started' | 'quality_monitoring' | 'packaging_preparation';
    deliveredSubStatus?: 'quality_inspection' | 'packaging_completed' | 'customer_confirmed';
  };

  // 交付计划信息
  deliveryPlan?: {
    materialRequirements: Array<{
      materialType: string;
      specifications: string;
      requiredQuantity: number;
      availableQuantity: number;
      shortfall: number;
    }>;
    capacityEvaluation: {
      requiredCapacity: number;
      availableCapacity: number;
      utilizationRate: number;
      bottleneckWorkstations: string[];
    };
    deliveryCommitment: {
      promisedDate: string;
      confidence: number;
      riskFactors: string[];
    };
  };

  // 生产排程信息
  productionSchedule?: {
    cuttingPlan: {
      selectedPlan: string;
      materialUtilization: number;
      estimatedTime: number;
      wastePercentage: number;
    };
    resourceAllocation: {
      assignedWorkers: Array<{
        name: string;
        role: string;
        workstation: string;
      }>;
      equipmentSchedule: Array<{
        equipment: string;
        timeSlot: string;
        utilization: number;
      }>;
    };
    timeline: {
      startTime: string;
      endTime: string;
      milestones: Array<{
        name: string;
        plannedTime: string;
        status: 'pending' | 'in_progress' | 'completed';
      }>;
    };
  };

  // 执行监控信息
  executionProgress?: {
    overallProgress: number;
    currentTask?: {
      name: string;
      assignee: string;
      location: string;
      startTime: string;
      progress: number;
    };
    qualityMetrics: {
      passRate: number;
      defectCount: number;
      reworkCount: number;
    };
    timeline: Array<{
      timestamp: string;
      event: string;
      details: string;
      operator: string;
    }>;
  };
}

/**
 * 生产工单明细
 */
export interface ProductionOrderItem {
  id: string;
  productionOrderId: string;
  customerOrderItemId: string; // 关联的客户订单明细ID
  specifications: {
    length: number;    // mm
    width: number;     // mm
    thickness: number; // mm
    glassType: 'clear' | 'tinted' | 'low_e' | 'reflective';
    color?: string;
  };
  quantity: number;
  processFlow: ProcessStep[];
  currentStatus: string;
  currentWorkstation?: string;
  utilizationRate?: number; // 排版利用率验证
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
}

/**
 * 验证用订单模型（保持向后兼容）
 * @deprecated 使用 CustomerOrder 替代
 */
export interface ValidationOrder extends CustomerOrder {
  items: ValidationOrderItem[];
}

/**
 * 验证用订单明细（保持向后兼容）
 * @deprecated 使用 CustomerOrderItem 替代
 */
export interface ValidationOrderItem extends CustomerOrderItem {
  orderId: string;
  processFlow: ProcessStep[];
  currentStatus: string;
  utilizationRate?: number;
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
}



/**
 * 工艺步骤
 */
export interface ProcessStep {
  stepName: string;
  workstation: string;
  workstationGroup?: string; // 工段分组：冷工段、钢化工段、合片工段
  estimatedDuration: number; // 分钟
  actualDuration?: number;   // 实际用时，用于效率验证
  constraints: Record<string, unknown>; // 工艺约束参数
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
}

// ==================== 排版优化相关 ====================

/**
 * 排版任务
 */
export interface CuttingTask {
  id: string;
  taskName: string;
  glassType: {
    material: string;
    thickness: number;
    color: string;
  };
  orderItems: ValidationOrderItem[];
  rawSheets: RawSheet[];
  status: 'pending' | 'optimizing' | 'completed' | 'executed';
  createdAt: string;
  optimizationResults?: OptimizationResult[];
}

/**
 * 原片规格
 */
export interface RawSheet {
  id: string;
  length: number;      // mm
  width: number;       // mm
  thickness: number;   // mm
  material: string;
  color: string;
  cost: number;        // 元/片
  availableQuantity: number;
  supplier?: string;
}

/**
 * 优化结果
 */
export interface OptimizationResult {
  id: string;
  taskId: string;
  algorithmType: 'internal' | 'third_party';
  algorithmName: string;
  utilizationRate: number;    // 利用率百分比
  totalSheets: number;        // 使用原片数量
  totalCost: number;          // 总成本
  cuttingPlans: CuttingPlan[];
  computationTime: number;    // 计算耗时(秒)
  createdAt: string;
  wasteArea: number;          // 废料面积(m²)
  savings?: number;           // 节省成本(元)
}

/**
 * 切割方案
 */
export interface CuttingPlan {
  id: string;
  sheetId: string;
  layout: CuttingLayout;
  pieces: CuttingPiece[];
  utilizationRate: number;
  wasteArea: number;          // m²
}

/**
 * 切割布局
 */
export interface CuttingLayout {
  sheetLength: number;        // mm
  sheetWidth: number;         // mm
  pieces: {
    x: number;                // mm
    y: number;                // mm
    length: number;           // mm
    width: number;            // mm
    orderItemId: string;
    rotation: boolean;
  }[];
}

/**
 * 切割片段
 */
export interface CuttingPiece {
  id: string;
  orderItemId: string;
  x: number;                  // mm
  y: number;                  // mm
  length: number;             // mm
  width: number;              // mm
  rotation: boolean;
}

// ==================== 工段调度相关 ====================

/**
 * 生产批次
 */
export interface ProductionBatch {
  id: string;
  batchNumber: string;
  workstation: string;
  processType: string;
  orderItems: ValidationOrderItem[];
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  plannedStartTime: string;
  plannedEndTime: string;
  actualStartTime?: string;
  actualEndTime?: string;
  operator?: string;
  equipment: string;
  processParameters: Record<string, unknown>;
  qualityRecords: QualityRecord[];
  efficiency?: number;        // 效率百分比
}

/**
 * 工段半成品
 */
export interface SemiFinishedProduct {
  id: string;
  orderItemId: string;
  currentWorkstation: string;
  specifications: {
    length: number;
    width: number;
    thickness: number;
    glassType: string;
  };
  quantity: number;
  status: 'available' | 'reserved' | 'in_process' | 'completed' | 'defective';
  location: string;
  qualityStatus: 'pending' | 'passed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

/**
 * 工段调度
 */
export interface WorkstationSchedule {
  id: string;
  workstationId: string;
  workstationName: string;
  date: string;
  shifts: WorkShift[];
  capacity: {
    maxBatches: number;
    maxPieces: number;
    maxArea: number;          // m²
  };
  utilization: {
    plannedBatches: number;
    plannedPieces: number;
    plannedArea: number;
    utilizationRate: number;  // 百分比
  };
  efficiency?: {
    traditional: number;
    optimized: number;
    improvement: number;
  };
}

/**
 * 班次安排
 */
export interface WorkShift {
  shiftId: string;
  shiftName: string;
  startTime: string;
  endTime: string;
  batches: ProductionBatch[];
  operators: string[];
  equipment: string[];
}

// ==================== 质量管理相关 ====================

/**
 * 质量记录
 */
export interface QualityRecord {
  id: string;
  orderItemId: string;
  batchId: string;
  processType: string;
  inspectionType: 'incoming' | 'in_process' | 'final';
  inspectionItems: InspectionItem[];
  inspector: string;
  inspectionDate: string;
  result: 'passed' | 'failed' | 'rework';
  defects?: DefectRecord[];
  notes?: string;
}

/**
 * 检验项目
 */
export interface InspectionItem {
  itemName: string;
  standard: string;
  actualValue: string;
  result: 'pass' | 'fail';
  tolerance: string;
}

/**
 * 缺陷记录
 */
export interface DefectRecord {
  defectType: string;
  severity: 'minor' | 'major' | 'critical';
  quantity: number;
  location: string;
  cause?: string;
  action: 'accept' | 'rework' | 'scrap';
}

// ==================== 工艺约束相关 ====================

/**
 * 切割工艺约束
 */
export interface CuttingConstraints {
  bladeWidth: number;         // mm，切割刀具宽度
  minCutLength: number;       // mm，最小切割长度
  maxCutLength: number;       // mm，最大切割长度
  edgeMargin: {
    top: number;              // mm
    bottom: number;           // mm
    left: number;             // mm
    right: number;            // mm
  };
  thicknessRange: {
    min: number;              // mm
    max: number;              // mm
  };
}

/**
 * 钢化工艺约束
 */
export interface TemperingConstraints {
  minSize: { length: number; width: number }; // mm
  maxSize: { length: number; width: number }; // mm
  thicknessRange: { min: number; max: number }; // mm
  batchConstraints: {
    maxWeight: number;        // kg
    thicknessTolerance: number; // mm
    temperatureRange: Record<string, { min: number; max: number }>;
  };
  deformationTolerance: {
    flatness: number;         // mm/m
    bowAndWarp: number;       // mm/m
  };
}

/**
 * 中空玻璃约束
 */
export interface InsulatedGlassConstraints {
  glassThicknessDiff: number; // mm
  spacerWidths: number[];     // mm
  sealantCuring: {
    structuralGlaze: {
      initialCure: number;    // 小时
      fullCure: number;       // 小时
    };
    secondarySeal: {
      initialCure: number;    // 小时
      fullCure: number;       // 小时
    };
  };
  gasFilling: {
    argonConcentration: number; // %
    fillingTime: number;        // 分钟
  };
}

// ==================== 价值指标相关 ====================

/**
 * 价值指标
 */
export interface ValueMetric {
  name: string;               // 指标名称
  target: number;             // 目标值
  actual: number;             // 实际值
  unit: string;               // 单位
  period: string;             // 统计周期
  source: 'traditional' | 'smart' | 'comparison';
  notes?: string;             // 备注
  improvement?: number;       // 改进百分比
}

// ==================== 异常处理相关 ====================

/**
 * 异常事件
 */
export interface ExceptionEvent {
  id: string;
  type: 'breakage' | 'shortage' | 'cancellation' | 'equipment';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedOrders: string[];
  affectedBatches: string[];
  status: 'detected' | 'processing' | 'resolved';
  detectedAt: string;
  resolvedAt?: string;
  notes?: string;
}

/**
 * 版本化实体（用于并发控制）
 */
export interface VersionedEntity {
  id: string;
  version: number;            // 每次写入+1，用于模拟并发冲突
}

// ==================== 算法策略相关 ====================

/**
 * 算法阶段与切换策略
 */
export interface AlgorithmStrategy {
  phase: 'validation' | 'trial' | 'full';
  primary: 'third_party' | 'internal';
  fallback: 'third_party' | 'internal';
  switchCriteria: {
    minUtilizationImprovement: number;
    maxComputationTimeSec: number;
    stabilityScore: number;
  };
  rollbackTriggers: string[];
  effectiveFrom: string;
}