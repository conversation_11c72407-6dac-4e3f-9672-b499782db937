<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="w-[98vw] h-[96vh] max-w-none flex flex-col p-0">
      <!-- 对话框头部 -->
      <DialogHeader class="flex-shrink-0 border-b px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <DialogTitle class="flex items-center gap-2 text-lg font-semibold">
              <Settings class="h-5 w-5 text-blue-600" />
              生产发布工作台
            </DialogTitle>
            <DialogDescription class="text-sm text-gray-600 mt-1">
              {{ workOrder?.workOrderNumber }} - {{ workOrder?.customerName }} | 工单发布前的决策支持与优化分析
            </DialogDescription>
          </div>

          <!-- 生产发布步骤 - 动态4步骤进度条 -->
          <div class="hidden lg:flex items-center gap-4 ml-6">
            <div class="flex items-center gap-2">
              <div
                v-for="(step, index) in releaseSteps"
                :key="step.key"
                class="flex items-center"
              >
                <div
                  class="flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium cursor-pointer transition-colors"
                  :class="getStepClass(step.key)"
                  @click="switchToStep(step.key)"
                  :title="step.name"
                >
                  <component :is="step.icon" class="h-3 w-3" />
                </div>
                <div
                  v-if="index < releaseSteps.length - 1"
                  class="w-8 h-0.5 mx-1 transition-colors"
                  :class="isStepCompleted(step.key) ? 'bg-green-500' : 'bg-gray-300'"
                ></div>
              </div>
            </div>
            <div class="ml-3 text-xs">
              <div class="font-medium text-blue-900">{{ getCurrentStepText() }}</div>
              <div class="text-blue-600">{{ getCurrentStepStatus() }}</div>
            </div>
          </div>
        </div>
      </DialogHeader>

      <div v-if="!workOrder" class="flex-1 flex items-center justify-center py-8">
        <div class="text-center text-gray-500">
          <Package class="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>工单信息加载中...</p>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div v-else class="flex-1 overflow-y-auto custom-scrollbar flex flex-col">
        <!-- 工单基本信息 - 改为单行水平布局 -->
        <div class="flex-shrink-0 px-6 py-3 border-b bg-gray-50">
          <div class="flex flex-wrap items-center gap-x-8 gap-y-2 text-sm">
            <div class="flex items-center gap-2">
              <span class="text-gray-500">工单号:</span>
              <span class="font-medium">WO-2024-0002</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">客户名称:</span>
              <span class="font-medium">万科集团</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">工单项数:</span>
              <span class="font-medium text-blue-600">3项</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">计划交期:</span>
              <span class="font-medium">2025/9/4</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">优先级:</span>
              <span class="font-medium text-orange-600">中</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-500">当前状态:</span>
              <span class="font-medium text-blue-600">已发布</span>
            </div>
          </div>
        </div>



        <!-- 主工作区域 -->
        <div class="flex-1">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-0">
            <!-- 左侧：工单详情和阶段操作 -->
            <div class="lg:col-span-2 flex flex-col border-r border-gray-200">
              <!-- 资源状态总览 -->
              <div class="flex-shrink-0 border-b border-gray-200">
                <ResourceStatusOverview :work-order="workOrder" />
              </div>

              <!-- 工单详情 -->
              <div class="flex-shrink-0 border-b border-gray-200">
                <div class="px-6 py-4">
                  <h3 class="font-medium text-gray-900 mb-4">工单详情</h3>
                  <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                      <thead class="sticky top-0 bg-white">
                        <tr class="border-b">
                          <th class="text-left py-2">序号</th>
                          <th class="text-left py-2">产品规格</th>
                          <th class="text-left py-2">数量</th>
                          <th class="text-left py-2">物料需求</th>
                          <th class="text-left py-2">预计用料</th>
                          <th class="text-left py-2">余料预测</th>
                          <th class="text-left py-2">工艺要求</th>
                          <th class="text-left py-2">状态</th>
                          <th class="text-left py-2">进度</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- 加载状态 -->
                        <tr v-if="isLoadingItems" class="border-b">
                          <td colspan="7" class="py-8 text-center text-gray-500">
                            <div class="flex items-center justify-center gap-2">
                              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                              加载工单详情中...
                            </div>
                          </td>
                        </tr>

                        <!-- 错误状态 -->
                        <tr v-else-if="loadError" class="border-b">
                          <td colspan="9" class="py-8 text-center text-red-500">
                            {{ loadError }}
                          </td>
                        </tr>

                        <!-- 无数据状态 -->
                        <tr v-else-if="workOrderItems.length === 0" class="border-b">
                          <td colspan="9" class="py-8 text-center text-gray-500">
                            暂无工单详情数据
                          </td>
                        </tr>

                        <!-- 动态数据行 -->
                        <tr v-else v-for="(item, index) in workOrderItems" :key="item.id" class="border-b">
                          <td class="py-2">{{ index + 1 }}</td>
                          <td class="py-2">
                            <div v-if="item.specifications.structure" class="text-xs">
                              <div class="font-medium">{{ item.specifications.structure }}</div>
                              <div class="text-gray-500">{{ item.specifications.length }}x{{ item.specifications.width }}</div>
                            </div>
                            <div v-else>
                              {{ item.specifications.thickness }}mm{{ getGlassTypeName(item.specifications.glassType) }}
                              {{ item.specifications.length }}x{{ item.specifications.width }}
                            </div>
                          </td>
                          <td class="py-2">{{ item.quantity }}片</td>
                          <td class="py-2">
                            <div class="text-xs">
                              <div>原片: {{ calculateRawGlassNeeded(item) }}片</div>
                              <div class="text-gray-500">型材: {{ calculateProfileNeeded(item) }}米</div>
                            </div>
                          </td>
                          <td class="py-2">
                            <div class="text-xs">
                              <div class="text-blue-600">{{ calculateMaterialCost(item) }}元</div>
                              <div class="text-gray-500">利用率: {{ calculateUtilizationRate(item) }}%</div>
                            </div>
                          </td>
                          <td class="py-2">
                            <div class="text-xs">
                              <div class="text-green-600">{{ calculateWasteMaterial(item) }}片</div>
                              <div class="text-gray-500">可复用</div>
                            </div>
                          </td>
                          <td class="py-2">
                            <div class="text-xs">
                              {{ item.processFlow.map(step => step.stepName).join('+') }}
                            </div>
                          </td>
                          <td class="py-2">
                            <Badge :variant="getStatusVariant(item.currentStatus)" size="sm">
                              {{ getStatusName(item.currentStatus) }}
                            </Badge>
                          </td>
                          <td class="py-2">
                            <div class="flex items-center gap-2">
                              <div class="w-12 bg-gray-200 rounded-full h-2">
                                <div
                                  class="h-2 rounded-full transition-all duration-300"
                                  :class="item.currentStatus === 'completed' ? 'bg-green-600' : 'bg-blue-600'"
                                  :style="{ width: `${Math.round((item.utilizationRate || 0) * 100)}%` }"
                                ></div>
                              </div>
                              <span class="text-xs">{{ Math.round((item.utilizationRate || 0) * 100) }}%</span>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <!-- 阶段操作面板 - 动态切换 -->
              <div class="flex-1">
                <div class="px-6 py-4 border-b border-gray-200">
                  <h3 class="font-medium text-gray-900">{{ getCurrentStepText() }} - 操作面板</h3>
                </div>

                <div class="px-6 py-4">
                  <!-- 动态步骤内容 -->
                  <div class="space-y-6">
                    <!-- 步骤一：工单构成审查 -->
                    <div v-if="currentStep === 'review'">
                      <WorkOrderReviewContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                      />
                    </div>

                    <!-- 步骤二：生产可行性分析 -->
                    <div v-else-if="currentStep === 'analysis'">
                      <ProductionAnalysisContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                      />
                    </div>

                    <!-- 步骤三：合并优化决策 -->
                    <div v-else-if="currentStep === 'optimization'">
                      <OptimizationDecisionContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                      />
                    </div>

                    <!-- 步骤四：决策与执行 -->
                    <div v-else-if="currentStep === 'execution'">
                      <ExecutionDecisionContent
                        :work-order="workOrder"
                        @step-completed="handleStepCompleted"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：决策支持面板 -->
            <div class="lg:col-span-1 flex flex-col">
              <DecisionSupportPanel :work-order="workOrder" :current-step="currentStep" />
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框底部 -->
      <DialogFooter class="flex-shrink-0 border-t px-6 py-4">
        <!-- 关键指标 - 移入footer区域 -->
        <div class="flex-1 grid grid-cols-4 gap-4 mr-6">
          <div class="text-center">
            <div class="text-lg font-bold text-blue-600">50%</div>
            <div class="text-xs text-gray-600">完成进度</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">95</div>
            <div class="text-xs text-gray-600">质量评分</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-orange-600">16</div>
            <div class="text-xs text-gray-600">剩余天数</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">正常</div>
            <div class="text-xs text-gray-600">交期状态</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <Button variant="outline" @click="$emit('update:open', false)">
          关闭
        </Button>
        <Button>
          <ArrowRight class="h-4 w-4 mr-2" />
          完成排程，开始执行
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Settings,
  Package,
  ClipboardList,
  Calendar,
  Play,
  ArrowRight
} from 'lucide-vue-next';
import { mesService } from '@/services/mesService';
import { productionReleaseService } from '@/services/productionReleaseService';
import type { ProductionOrderItem } from '@/types/mes-validation';

// 导入生产发布步骤组件
import WorkOrderReviewContent from './release-steps/WorkOrderReviewContent.vue';
import ProductionAnalysisContent from './release-steps/ProductionAnalysisContent.vue';
import OptimizationDecisionContent from './release-steps/OptimizationDecisionContent.vue';
import ExecutionDecisionContent from './release-steps/ExecutionDecisionContent.vue';
import ResourceStatusOverview from './ResourceStatusOverview.vue';
import DecisionSupportPanel from './DecisionSupportPanel.vue';

interface Props {
  open: boolean;
  workOrder?: any;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'step-updated', workOrderId: string, step: ReleaseStep): void;
  (e: 'work-order-released', workOrderId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 工单详情数据状态
const workOrderItems = ref<ProductionOrderItem[]>([]);
const isLoadingItems = ref(false);
const loadError = ref<string | null>(null);

// 生产发布步骤管理
type ReleaseStep = 'review' | 'analysis' | 'optimization' | 'execution';
const currentStep = ref<ReleaseStep>('review'); // 默认为工单构成审查

// 发布步骤定义
const releaseSteps = [
  { key: 'review' as ReleaseStep, name: '工单构成审查', icon: ClipboardList },
  { key: 'analysis' as ReleaseStep, name: '生产可行性分析', icon: Calendar },
  { key: 'optimization' as ReleaseStep, name: '合并优化决策', icon: Settings },
  { key: 'execution' as ReleaseStep, name: '决策与执行', icon: Play }
];

// 加载工单详情数据
const loadWorkOrderItems = async () => {
  if (!props.workOrder) {
    workOrderItems.value = [];
    return;
  }

  isLoadingItems.value = true;
  loadError.value = null;

  try {
    // 优先使用生产发布服务的Mock数据
    const mockItems = await productionReleaseService.getWorkOrderDetails(props.workOrder.id);

    if (mockItems && mockItems.length > 0) {
      // 转换Mock数据格式为组件需要的格式
      workOrderItems.value = mockItems.map(item => ({
        id: item.id,
        productionOrderId: item.productionOrderId,
        customerOrderItemId: item.customerOrderItemId,
        specifications: {
          ...item.specifications,
          glassType: item.specifications.glassType as "clear" | "tinted" | "low_e" | "reflective"
        },
        quantity: item.quantity,
        processFlow: item.processFlow.map(step => ({
          ...step,
          status: step.status as "pending" | "in_progress" | "completed" | "skipped"
        })),
        currentStatus: item.currentStatus,
        currentWorkstation: item.processFlow.find(step => step.status === 'in_progress')?.workstation,
        utilizationRate: item.utilizationRate
      }));
    } else if (props.workOrder.items && Array.isArray(props.workOrder.items)) {
      // 如果Mock数据不可用，使用传入的workOrder.items
      workOrderItems.value = props.workOrder.items;
    } else {
      // 最后尝试从MES服务获取完整的工单数据
      const allWorkOrders = await mesService.getProductionOrders();
      const fullWorkOrder = allWorkOrders.find(wo => wo.id === props.workOrder.id || wo.workOrderNumber === props.workOrder.workOrderNumber);

      if (fullWorkOrder && fullWorkOrder.items) {
        workOrderItems.value = fullWorkOrder.items;
      } else {
        // 使用内置模拟数据作为后备
        workOrderItems.value = generateMockWorkOrderItems();
      }
    }
  } catch (error) {
    console.error('加载工单详情失败:', error);
    loadError.value = '加载工单详情失败';
    // 使用内置模拟数据作为后备
    workOrderItems.value = generateMockWorkOrderItems();
  } finally {
    isLoadingItems.value = false;
  }
};

// 生成模拟工单详情数据（作为后备）
const generateMockWorkOrderItems = (): ProductionOrderItem[] => {
  return [
    {
      id: 'WOI-001',
      productionOrderId: props.workOrder?.id || 'WO-2024-0002',
      customerOrderItemId: 'COI-001',
      specifications: {
        length: 1200,
        width: 800,
        thickness: 6,
        glassType: 'clear'
      },
      quantity: 50,
      processFlow: [
        { stepName: '钢化', workstation: '钢化炉#1', estimatedDuration: 120, status: 'in_progress', constraints: {} },
        { stepName: '磨边', workstation: '磨边机#2', estimatedDuration: 60, status: 'pending', constraints: {} }
      ],
      currentStatus: 'in_progress',
      currentWorkstation: '钢化炉#1',
      utilizationRate: 0.75
    },
    {
      id: 'WOI-002',
      productionOrderId: props.workOrder?.id || 'WO-2024-0002',
      customerOrderItemId: 'COI-002',
      specifications: {
        length: 1500,
        width: 1000,
        thickness: 8,
        glassType: 'low_e'
      },
      quantity: 30,
      processFlow: [
        { stepName: '夹胶', workstation: '夹胶线#1', estimatedDuration: 180, status: 'pending', constraints: {} },
        { stepName: '打孔', workstation: '打孔机#1', estimatedDuration: 90, status: 'pending', constraints: {} }
      ],
      currentStatus: 'pending',
      utilizationRate: 0.25
    },
    {
      id: 'WOI-003',
      productionOrderId: props.workOrder?.id || 'WO-2024-0002',
      customerOrderItemId: 'COI-003',
      specifications: {
        length: 2000,
        width: 1200,
        thickness: 10,
        glassType: 'clear'
      },
      quantity: 20,
      processFlow: [
        { stepName: '中空', workstation: '中空线#1', estimatedDuration: 150, status: 'completed', constraints: {} },
        { stepName: '密封', workstation: '密封机#1', estimatedDuration: 45, status: 'completed', constraints: {} }
      ],
      currentStatus: 'completed',
      utilizationRate: 1.0
    }
  ];
};

// 初始化当前步骤
const initializeCurrentStep = () => {
  if (!props.workOrder) return;

  // 根据工单状态确定当前步骤
  switch (props.workOrder.status) {
    case 'pending':
      currentStep.value = 'review'; // 待发布工单从审查开始
      break;
    case 'released':
      currentStep.value = 'execution'; // 已发布工单显示执行步骤
      break;
    case 'in_progress':
      currentStep.value = 'execution'; // 进行中工单显示执行步骤
      break;
    case 'completed':
      currentStep.value = 'execution'; // 已完成工单显示执行步骤
      break;
    default:
      currentStep.value = 'review'; // 默认从审查开始
  }
};

// 监听workOrder变化，重新加载数据
watch(() => props.workOrder, () => {
  if (props.open) {
    loadWorkOrderItems();
    initializeCurrentStep();
  }
}, { immediate: true });

// 监听对话框打开状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    loadWorkOrderItems();
    initializeCurrentStep();
  }
});



// 获取玻璃类型中文名称
const getGlassTypeName = (glassType: string): string => {
  const typeMap: Record<string, string> = {
    'clear': '透明玻璃',
    'tinted': '有色玻璃',
    'low_e': 'Low-E玻璃',
    'reflective': '反射玻璃'
  };
  return typeMap[glassType] || '透明玻璃';
};

// 获取状态徽章样式
const getStatusVariant = (status: string): "default" | "destructive" | "outline" | "secondary" => {
  const statusMap: Record<string, "default" | "destructive" | "outline" | "secondary"> = {
    'pending': 'secondary',
    'in_progress': 'default',
    'completed': 'outline',
    'cancelled': 'destructive',
    '待开始': 'secondary',
    '进行中': 'default',
    '已完成': 'outline',
    '已取消': 'destructive'
  };
  return statusMap[status] || 'secondary';
};

// 获取状态中文名称
const getStatusName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 计算原片需求
const calculateRawGlassNeeded = (item: any): number => {
  // 简化计算：根据产品尺寸和数量估算原片需求
  const area = (item.specifications.length * item.specifications.width) / 1000000; // 转换为平方米
  const totalArea = area * item.quantity;
  // 假设标准原片为3.3m x 2.14m = 7.062平方米
  const standardSheetArea = 7.062;
  return Math.ceil(totalArea / standardSheetArea);
};

// 计算型材需求
const calculateProfileNeeded = (item: any): number => {
  // 简化计算：根据产品周长估算型材需求
  const perimeter = 2 * (item.specifications.length + item.specifications.width) / 1000; // 转换为米
  return Math.round(perimeter * item.quantity * 1.1); // 加10%损耗
};

// 计算物料成本
const calculateMaterialCost = (item: any): number => {
  const rawGlassNeeded = calculateRawGlassNeeded(item);
  const profileNeeded = calculateProfileNeeded(item);
  // 简化成本计算
  const glassCost = rawGlassNeeded * 450; // 假设每片原片450元
  const profileCost = profileNeeded * 25; // 假设每米型材25元
  return Math.round(glassCost + profileCost);
};

// 计算利用率
const calculateUtilizationRate = (item: any): number => {
  // 简化计算：基于产品尺寸与标准原片的匹配度
  const productArea = (item.specifications.length * item.specifications.width) / 1000000;
  const standardSheetArea = 7.062;
  const utilizationRate = Math.min(productArea / standardSheetArea * 100, 95);
  return Math.round(utilizationRate);
};

// 计算余料
const calculateWasteMaterial = (item: any): number => {
  const rawGlassNeeded = calculateRawGlassNeeded(item);
  const utilizationRate = calculateUtilizationRate(item) / 100;
  const wasteRate = 1 - utilizationRate;
  return Math.round(rawGlassNeeded * wasteRate);
};

// 步骤管理方法
const getStepClass = (stepKey: ReleaseStep): string => {
  const stepOrder: ReleaseStep[] = ['review', 'analysis', 'optimization', 'execution'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepKey);

  if (stepIndex < currentIndex) {
    return 'bg-green-100 text-green-600'; // 已完成
  } else if (stepIndex === currentIndex) {
    return 'bg-blue-100 text-blue-600'; // 当前步骤
  } else {
    return 'bg-gray-100 text-gray-400'; // 未开始
  }
};

const isStepCompleted = (stepKey: ReleaseStep): boolean => {
  const stepOrder: ReleaseStep[] = ['review', 'analysis', 'optimization', 'execution'];
  const currentIndex = stepOrder.indexOf(currentStep.value);
  const stepIndex = stepOrder.indexOf(stepKey);
  return stepIndex < currentIndex;
};

const getCurrentStepText = (): string => {
  const step = releaseSteps.find(s => s.key === currentStep.value);
  return step?.name || '未知步骤';
};

const getCurrentStepStatus = (): string => {
  const statusMap: Record<ReleaseStep, string> = {
    'review': '审查中',
    'analysis': '分析中',
    'optimization': '优化中',
    'execution': '执行中'
  };
  return statusMap[currentStep.value] || '未知状态';
};

const switchToStep = (step: ReleaseStep) => {
  currentStep.value = step;
  // 通知父组件步骤变更
  if (props.workOrder) {
    emit('step-updated', props.workOrder.id, step);
  }
};

const handleStepCompleted = (completedStep: string) => {
  const stepOrder: ReleaseStep[] = ['review', 'analysis', 'optimization', 'execution'];
  const currentIndex = stepOrder.indexOf(completedStep as ReleaseStep);

  if (currentIndex >= 0 && currentIndex < stepOrder.length - 1) {
    // 自动切换到下一个步骤
    const nextStep = stepOrder[currentIndex + 1];
    currentStep.value = nextStep;

    // 通知父组件步骤变更
    if (props.workOrder) {
      emit('step-updated', props.workOrder.id, nextStep);
    }
  } else if (completedStep === 'execution') {
    // 所有步骤已完成，工单可以发布
    if (props.workOrder) {
      emit('step-updated', props.workOrder.id, 'execution');
      emit('work-order-released', props.workOrder.id);
    }
  }
};
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
