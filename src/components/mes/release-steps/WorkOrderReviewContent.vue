<template>
  <div class="space-y-6">
    <!-- 步骤说明和操作指导 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <ClipboardList class="h-4 w-4 text-blue-600" />
          <h4 class="font-medium text-blue-900">工单构成审查</h4>
        </div>
        <Button variant="ghost" size="sm" @click="showHelp = !showHelp">
          <HelpCircle class="h-4 w-4" />
        </Button>
      </div>
      <p class="text-sm text-blue-700">审查工单的订单项构成，确认技术规格与工艺要求，验证BOM完整性</p>

      <!-- 操作指导 -->
      <div v-if="showHelp" class="mt-3 p-3 bg-blue-100 rounded-lg">
        <h5 class="text-sm font-medium text-blue-900 mb-2">操作指导：</h5>
        <ul class="text-xs text-blue-800 space-y-1">
          <li>• 检查每个订单项的技术规格是否完整准确</li>
          <li>• 确认工艺要求与公司生产能力匹配</li>
          <li>• 验证BOM清单的完整性和准确性</li>
          <li>• 标记任何需要澄清或修正的问题项</li>
        </ul>
      </div>
    </div>

    <!-- 关联订单项聚合视图 -->
    <div class="border border-gray-200 rounded-lg">
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h4 class="font-medium text-gray-900 flex items-center">
          <Package class="h-4 w-4 mr-2" />
          关联订单项聚合视图
        </h4>
      </div>
      
      <div class="p-4">
        <!-- 订单项追溯 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">订单项追溯</h5>
          <div v-if="isLoading" class="text-center py-4 text-gray-500">
            加载中...
          </div>
          <div v-else-if="reviewData?.orderGroups" class="space-y-2">
            <div
              v-for="group in reviewData.orderGroups"
              :key="group.customerOrderNumber"
              class="space-y-2"
            >
              <div
                v-for="item in group.items"
                :key="item.id"
                class="flex items-center justify-between p-3 rounded-lg"
                :class="item.currentStatus === 'pending' ? 'bg-orange-50 border border-orange-200' : 'bg-green-50 border border-green-200'"
              >
                <div class="flex items-center gap-3">
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="item.currentStatus === 'pending' ? 'bg-orange-500' : 'bg-green-500'"
                  ></div>
                  <div>
                    <div
                      class="text-sm font-medium"
                      :class="item.currentStatus === 'pending' ? 'text-orange-900' : 'text-green-900'"
                    >
                      {{ group.customerOrderNumber }} - {{ group.projectName }}
                    </div>
                    <div
                      class="text-xs"
                      :class="item.currentStatus === 'pending' ? 'text-orange-700' : 'text-green-700'"
                    >
                      {{ item.specifications.thickness }}mm{{ item.specifications.glassType === 'clear' ? '透明' : '有色' }}玻璃
                      {{ item.specifications.length }}x{{ item.specifications.width }} ({{ item.quantity }}片)
                    </div>
                  </div>
                </div>
                <Badge
                  :variant="item.currentStatus === 'pending' ? 'secondary' : 'outline'"
                  size="sm"
                >
                  {{ item.currentStatus === 'pending' ? '待确认' : '已确认' }}
                </Badge>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-4 text-gray-500">
            暂无数据
          </div>
        </div>

        <!-- 技术规格与工艺确认 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium text-gray-800 mb-3">技术规格与工艺确认</h5>
          <div v-if="isLoading" class="text-center py-4 text-gray-500">
            加载中...
          </div>
          <div v-else-if="reviewData?.technicalSpecifications" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 玻璃规格 -->
            <div class="p-3 border border-gray-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-900">玻璃规格</span>
                <component
                  :is="getStatusIcon(reviewData.technicalSpecifications.glassSpecs.status)"
                  class="h-4 w-4"
                  :class="getStatusIconClass(reviewData.technicalSpecifications.glassSpecs.status)"
                />
              </div>
              <div class="space-y-1 text-xs text-gray-600">
                <div>厚度: {{ reviewData.technicalSpecifications.glassSpecs.details.thickness.join(', ') }}</div>
                <div>类型: {{ reviewData.technicalSpecifications.glassSpecs.details.types.join(', ') }}</div>
                <div>尺寸: {{ reviewData.technicalSpecifications.glassSpecs.details.sizes }}</div>
                <div>标准: {{ reviewData.technicalSpecifications.glassSpecs.details.standards }}</div>
              </div>
            </div>

            <!-- 工艺要求 -->
            <div class="p-3 border border-gray-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-900">工艺要求</span>
                <component
                  :is="getStatusIcon(reviewData.technicalSpecifications.processRequirements.status)"
                  class="h-4 w-4"
                  :class="getStatusIconClass(reviewData.technicalSpecifications.processRequirements.status)"
                />
              </div>
              <div class="space-y-1 text-xs text-gray-600">
                <div>切割: {{ reviewData.technicalSpecifications.processRequirements.details.cutting }}</div>
                <div>磨边: {{ reviewData.technicalSpecifications.processRequirements.details.edging }}</div>
                <div>表面处理: {{ reviewData.technicalSpecifications.processRequirements.details.surfaceTreatment }}</div>
                <div>公差: {{ reviewData.technicalSpecifications.processRequirements.details.tolerance }}</div>
              </div>
            </div>

            <!-- 质量标准 -->
            <div class="p-3 border border-gray-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-900">质量标准</span>
                <component
                  :is="getStatusIcon(reviewData.technicalSpecifications.qualityStandards.status)"
                  class="h-4 w-4"
                  :class="getStatusIconClass(reviewData.technicalSpecifications.qualityStandards.status)"
                />
              </div>
              <div class="space-y-1 text-xs text-gray-600">
                <div>平整度: {{ reviewData.technicalSpecifications.qualityStandards.details.flatness }}</div>
                <div>边缘质量: {{ reviewData.technicalSpecifications.qualityStandards.details.edgeQuality }}</div>
                <div
                  :class="reviewData.technicalSpecifications.qualityStandards.status === 'pending' ? 'text-orange-600' : 'text-gray-600'"
                >
                  {{ reviewData.technicalSpecifications.qualityStandards.details.specialRequirements }}
                </div>
                <div>检验标准: {{ reviewData.technicalSpecifications.qualityStandards.details.inspectionStandard }}</div>
              </div>
            </div>

            <!-- 包装要求 -->
            <div class="p-3 border border-gray-200 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-900">包装要求</span>
                <component
                  :is="getStatusIcon(reviewData.technicalSpecifications.packagingRequirements.status)"
                  class="h-4 w-4"
                  :class="getStatusIconClass(reviewData.technicalSpecifications.packagingRequirements.status)"
                />
              </div>
              <div class="space-y-1 text-xs text-gray-600">
                <div>包装方式: {{ reviewData.technicalSpecifications.packagingRequirements.details.method }}</div>
                <div>保护材料: {{ reviewData.technicalSpecifications.packagingRequirements.details.protection }}</div>
                <div>标识要求: {{ reviewData.technicalSpecifications.packagingRequirements.details.labeling }}</div>
                <div>堆叠限制: {{ reviewData.technicalSpecifications.packagingRequirements.details.stackingLimit }}</div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-4 text-gray-500">
            暂无技术规格数据
          </div>
        </div>

        <!-- BOM校验 -->
        <div>
          <h5 class="text-sm font-medium text-gray-800 mb-3">BOM校验</h5>
          <div v-if="isLoading" class="text-center py-4 text-gray-500">
            加载中...
          </div>
          <div v-else-if="reviewData?.bomValidation?.items" class="space-y-3">
            <div
              v-for="item in reviewData.bomValidation.items"
              :key="item.id"
              class="flex items-center gap-3 p-3 rounded-lg"
              :class="getBomItemClass(item.status)"
            >
              <component
                :is="getBomStatusIcon(item.status)"
                class="h-4 w-4"
                :class="getBomStatusIconClass(item.status)"
              />
              <div class="flex-1">
                <div
                  class="text-sm font-medium"
                  :class="getBomItemTextClass(item.status)"
                >
                  {{ item.materialType }}
                </div>
                <div
                  class="text-xs"
                  :class="getBomItemSubTextClass(item.status)"
                >
                  {{ item.materialName }} {{ item.specification }} - 需要{{ item.requiredQuantity }}{{ item.unit }}
                </div>
                <div
                  class="text-xs text-gray-500 mt-1"
                >
                  供应商: {{ item.supplier }}
                </div>
              </div>
              <Badge
                :variant="getBomBadgeVariant(item.status)"
                size="sm"
              >
                {{ item.stockStatus }}
              </Badge>
            </div>
          </div>
          <div v-else class="text-center py-4 text-gray-500">
            暂无BOM数据
          </div>
        </div>
      </div>
    </div>

    <!-- 审查结果汇总 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3">审查结果汇总</h4>
      <div v-if="reviewData" class="grid grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-green-600">{{ reviewData.totalItems - reviewData.pendingItems }}</div>
          <div class="text-xs text-gray-600">已确认订单项</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-orange-600">{{ reviewData.pendingItems }}</div>
          <div class="text-xs text-gray-600">待确认项目</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-blue-600">{{ reviewData.bomValidation?.summary?.completeness || 95 }}%</div>
          <div class="text-xs text-gray-600">BOM完整度</div>
        </div>
      </div>
      <div v-else class="grid grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-gray-400">-</div>
          <div class="text-xs text-gray-600">已确认订单项</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-gray-400">-</div>
          <div class="text-xs text-gray-600">待确认项目</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-gray-400">-</div>
          <div class="text-xs text-gray-600">BOM完整度</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 mb-3 flex items-center">
        <Zap class="h-4 w-4 mr-2" />
        快速操作
      </h4>
      <div class="grid grid-cols-2 gap-2 mb-4">
        <Button variant="outline" size="sm" @click="handleQuickFix">
          <Settings class="h-4 w-4 mr-2" />
          一键修复问题
        </Button>
        <Button variant="outline" size="sm" @click="handleAutoConfirm">
          <CheckCircle class="h-4 w-4 mr-2" />
          自动确认规格
        </Button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      <Button class="flex-1" @click="completeReview" :disabled="hasUnconfirmedItems">
        <Calendar class="h-4 w-4 mr-2" />
        确认审查，进入可行性分析
      </Button>
      <Button variant="outline" class="flex-1" @click="handleMarkIssues">
        <AlertTriangle class="h-4 w-4 mr-2" />
        标记问题项
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { productionReleaseService } from '@/services/productionReleaseService';
import {
  Package,
  AlertTriangle,
  Calendar,
  ClipboardList,
  HelpCircle,
  Zap,
  Settings,
  CheckCircle
} from 'lucide-vue-next';

interface Props {
  workOrder?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'step-completed': [step: string];
}>();

// 审查数据
const reviewData = ref<any>(null);
const isLoading = ref(false);
const showHelp = ref(false);

// 加载审查数据
const loadReviewData = async () => {
  if (!props.workOrder?.id) return;

  isLoading.value = true;
  try {
    reviewData.value = await productionReleaseService.getWorkOrderReviewData(props.workOrder.id);
  } catch (error) {
    console.error('加载工单审查数据失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 检查是否有未确认项目
const hasUnconfirmedItems = computed(() => {
  if (!reviewData.value) return true;

  // 检查订单项是否有待确认的
  const hasPendingItems = reviewData.value.pendingItems > 0;

  // 检查技术规格是否有待确认的
  const hasPendingSpecs = reviewData.value.technicalSpecifications &&
    Object.values(reviewData.value.technicalSpecifications).some((spec: any) => spec.status === 'pending');

  return hasPendingItems || hasPendingSpecs;
});

// 状态图标辅助方法
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'confirmed': return 'Check';
    case 'pending': return 'AlertTriangle';
    case 'rejected': return 'X';
    default: return 'AlertTriangle';
  }
};

const getStatusIconClass = (status: string) => {
  switch (status) {
    case 'confirmed': return 'text-green-600';
    case 'pending': return 'text-orange-600';
    case 'rejected': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

// BOM相关辅助方法
const getBomItemClass = (status: string) => {
  switch (status) {
    case 'sufficient': return 'bg-green-50 border border-green-200';
    case 'pending': return 'bg-orange-50 border border-orange-200';
    case 'shortage': return 'bg-red-50 border border-red-200';
    default: return 'bg-gray-50 border border-gray-200';
  }
};

const getBomStatusIcon = (status: string) => {
  switch (status) {
    case 'sufficient': return 'Check';
    case 'pending': return 'AlertTriangle';
    case 'shortage': return 'X';
    default: return 'AlertTriangle';
  }
};

const getBomStatusIconClass = (status: string) => {
  switch (status) {
    case 'sufficient': return 'text-green-600';
    case 'pending': return 'text-orange-600';
    case 'shortage': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

const getBomItemTextClass = (status: string) => {
  switch (status) {
    case 'sufficient': return 'text-green-900';
    case 'pending': return 'text-orange-900';
    case 'shortage': return 'text-red-900';
    default: return 'text-gray-900';
  }
};

const getBomItemSubTextClass = (status: string) => {
  switch (status) {
    case 'sufficient': return 'text-green-700';
    case 'pending': return 'text-orange-700';
    case 'shortage': return 'text-red-700';
    default: return 'text-gray-700';
  }
};

const getBomBadgeVariant = (status: string): "default" | "destructive" | "outline" | "secondary" => {
  switch (status) {
    case 'sufficient': return 'outline';
    case 'pending': return 'secondary';
    case 'shortage': return 'destructive';
    default: return 'secondary';
  }
};

const completeReview = () => {
  if (!hasUnconfirmedItems.value) {
    emit('step-completed', 'review');
  }
};

// 快速操作方法
const handleQuickFix = () => {
  // 模拟一键修复问题
  console.log('执行一键修复问题');
  // 这里可以添加具体的修复逻辑
};

const handleAutoConfirm = () => {
  // 模拟自动确认规格
  console.log('执行自动确认规格');
  // 这里可以添加自动确认逻辑
};

const handleMarkIssues = () => {
  // 模拟标记问题项
  console.log('标记问题项');
  // 这里可以添加标记问题的逻辑
};

onMounted(() => {
  loadReviewData();
});

// 监听props变化
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder) {
    loadReviewData();
  }
}, { immediate: true });
</script>
