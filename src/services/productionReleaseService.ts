/**
 * 生产发布工作台数据服务
 * 负责加载和管理生产发布相关的Mock数据
 */

export interface WorkOrderItem {
  id: string;
  productionOrderId: string;
  customerOrderItemId: string;
  customerOrderNumber: string;
  customerName: string;
  projectName: string;
  specifications: {
    length: number;
    width: number;
    thickness: number;
    glassType: string;
  };
  quantity: number;
  processFlow: Array<{
    stepName: string;
    workstation: string;
    estimatedDuration: number;
    status: string;
    constraints: Record<string, any>;
  }>;
  currentStatus: string;
  utilizationRate: number;
  deliveryDate: string;
  priority: string;
}

export interface MaterialStock {
  materialCode: string;
  materialName: string;
  specification: string;
  currentStock: number;
  unit: string;
  unitCost: number;
  supplier: string;
  leadTime: number;
}

export interface WorkstationCapacity {
  name: string;
  currentLoad: number;
  maxCapacity: number;
  availableCapacity: number;
  status: 'available' | 'tight' | 'overloaded';
}

export interface OptimizationSuggestion {
  canMergeWith: string[];
  mergeRecommendation: {
    type: string;
    description: string;
    benefits: {
      efficiencyImprovement: number;
      timeSaving: number;
      costReduction: number;
      materialUtilization: number;
    };
    mergeDetails: {
      targetWorkOrder: string;
      targetCustomer: string;
      targetItems: number;
      combinedQuantity: number;
      estimatedDuration: number;
      materialUtilization: number;
    };
  };
}

export interface TechnicalSpecification {
  glassSpecs: {
    status: 'confirmed' | 'pending' | 'rejected';
    details: {
      thickness: string[];
      types: string[];
      sizes: string;
      standards: string;
    };
  };
  processRequirements: {
    status: 'confirmed' | 'pending' | 'rejected';
    details: {
      cutting: string;
      edging: string;
      surfaceTreatment: string;
      tolerance: string;
    };
  };
  qualityStandards: {
    status: 'confirmed' | 'pending' | 'rejected';
    details: {
      flatness: string;
      edgeQuality: string;
      specialRequirements: string;
      inspectionStandard: string;
    };
  };
  packagingRequirements: {
    status: 'confirmed' | 'pending' | 'rejected';
    details: {
      method: string;
      protection: string;
      labeling: string;
      stackingLimit: string;
    };
  };
}

export interface BomValidationItem {
  id: string;
  materialType: string;
  materialName: string;
  specification: string;
  requiredQuantity: number;
  unit: string;
  status: 'sufficient' | 'pending' | 'shortage';
  stockStatus: string;
  supplier: string;
}

export interface BomValidation {
  items: BomValidationItem[];
  summary: {
    totalItems: number;
    sufficientItems: number;
    pendingItems: number;
    completeness: number;
  };
}

export interface ProductionReleaseData {
  workOrderDetails: Record<string, {
    id: string;
    workOrderNumber: string;
    customerName: string;
    customerOrderId: string;
    customerOrderNumber: string;
    items: WorkOrderItem[];
  }>;
  technicalSpecifications: Record<string, TechnicalSpecification>;
  bomValidation: Record<string, BomValidation>;
  materialStock: Record<string, MaterialStock>;
  workstationCapacity: Record<string, WorkstationCapacity>;
  optimizationSuggestions: Record<string, OptimizationSuggestion>;
}

class ProductionReleaseService {
  private mockData: ProductionReleaseData | null = null;

  /**
   * 加载Mock数据
   */
  private async loadMockData(): Promise<ProductionReleaseData> {
    if (this.mockData) {
      return this.mockData;
    }

    try {
      const response = await fetch('/mock/mes/production-release-workbench.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      this.mockData = await response.json();
      return this.mockData!;
    } catch (error) {
      console.error('加载生产发布工作台Mock数据失败:', error);
      // 返回默认数据
      return this.getDefaultData();
    }
  }

  /**
   * 获取默认数据（当Mock文件加载失败时使用）
   */
  private getDefaultData(): ProductionReleaseData {
    return {
      workOrderDetails: {},
      technicalSpecifications: {},
      bomValidation: {},
      materialStock: {},
      workstationCapacity: {},
      optimizationSuggestions: {}
    };
  }

  /**
   * 获取工单详情数据
   */
  async getWorkOrderDetails(workOrderId: string): Promise<WorkOrderItem[]> {
    const data = await this.loadMockData();
    const workOrderDetail = data.workOrderDetails[workOrderId];
    return workOrderDetail?.items || [];
  }

  /**
   * 获取物料库存数据
   */
  async getMaterialStock(): Promise<Record<string, MaterialStock>> {
    const data = await this.loadMockData();
    return data.materialStock;
  }

  /**
   * 获取工段产能数据
   */
  async getWorkstationCapacity(): Promise<Record<string, WorkstationCapacity>> {
    const data = await this.loadMockData();
    return data.workstationCapacity;
  }

  /**
   * 获取技术规格数据
   */
  async getTechnicalSpecifications(workOrderId: string): Promise<TechnicalSpecification | null> {
    const data = await this.loadMockData();
    return data.technicalSpecifications[workOrderId] || null;
  }

  /**
   * 获取BOM验证数据
   */
  async getBomValidation(workOrderId: string): Promise<BomValidation | null> {
    const data = await this.loadMockData();
    return data.bomValidation[workOrderId] || null;
  }

  /**
   * 获取优化建议
   */
  async getOptimizationSuggestions(workOrderId: string): Promise<OptimizationSuggestion | null> {
    const data = await this.loadMockData();
    return data.optimizationSuggestions[workOrderId] || null;
  }

  /**
   * 获取工单的关联订单项（用于步骤一：工单构成审查）
   */
  async getWorkOrderReviewData(workOrderId: string) {
    const [items, technicalSpecs, bomValidation] = await Promise.all([
      this.getWorkOrderDetails(workOrderId),
      this.getTechnicalSpecifications(workOrderId),
      this.getBomValidation(workOrderId)
    ]);

    // 按客户订单分组
    const orderGroups = items.reduce((groups, item) => {
      const key = `${item.customerOrderNumber}-${item.customerName}`;
      if (!groups[key]) {
        groups[key] = {
          customerOrderNumber: item.customerOrderNumber,
          customerName: item.customerName,
          projectName: item.projectName,
          items: []
        };
      }
      groups[key].items.push(item);
      return groups;
    }, {} as Record<string, any>);

    return {
      orderGroups: Object.values(orderGroups),
      totalItems: items.length,
      confirmedItems: items.filter(item => item.currentStatus !== 'pending').length,
      pendingItems: items.filter(item => item.currentStatus === 'pending').length,
      technicalSpecifications: technicalSpecs,
      bomValidation: bomValidation
    };
  }

  /**
   * 获取生产可行性分析数据（用于步骤二）
   */
  async getProductionAnalysisData(workOrderId: string) {
    const [materialStock, workstationCapacity, items] = await Promise.all([
      this.getMaterialStock(),
      this.getWorkstationCapacity(),
      this.getWorkOrderDetails(workOrderId)
    ]);

    // 计算物料需求
    const materialRequirements = this.calculateMaterialRequirements(items);
    
    // 检查短缺物料
    const shortageItems = this.checkMaterialShortage(materialRequirements, materialStock);

    return {
      materialStock,
      workstationCapacity,
      materialRequirements,
      shortageItems,
      feasibilityScore: this.calculateFeasibilityScore(shortageItems, workstationCapacity)
    };
  }

  /**
   * 计算物料需求
   */
  private calculateMaterialRequirements(items: WorkOrderItem[]) {
    const requirements: Record<string, { needed: number; available: number; shortage: number }> = {};
    
    // 简化的物料需求计算逻辑
    items.forEach(item => {
      const { thickness, glassType } = item.specifications;
      const materialKey = `raw_glass_${thickness}mm_${glassType}`;
      
      if (!requirements[materialKey]) {
        requirements[materialKey] = { needed: 0, available: 0, shortage: 0 };
      }
      
      // 简化计算：每200片需要1片原片
      requirements[materialKey].needed += Math.ceil(item.quantity / 200);
    });

    return requirements;
  }

  /**
   * 检查物料短缺
   */
  private checkMaterialShortage(requirements: Record<string, any>, stock: Record<string, MaterialStock>) {
    const shortageItems: Array<{
      materialCode: string;
      materialName: string;
      needed: number;
      available: number;
      shortage: number;
      supplier: string;
      leadTime: number;
    }> = [];

    Object.entries(requirements).forEach(([key, req]) => {
      const stockItem = Object.values(stock).find(s => 
        s.materialCode.toLowerCase().includes(key.replace('raw_glass_', '').replace('mm_', 'mm-'))
      );
      
      if (stockItem) {
        req.available = stockItem.currentStock;
        req.shortage = Math.max(0, req.needed - stockItem.currentStock);
        
        if (req.shortage > 0) {
          shortageItems.push({
            materialCode: stockItem.materialCode,
            materialName: stockItem.materialName,
            needed: req.needed,
            available: req.available,
            shortage: req.shortage,
            supplier: stockItem.supplier,
            leadTime: stockItem.leadTime
          });
        }
      }
    });

    return shortageItems;
  }

  /**
   * 计算可行性评分
   */
  private calculateFeasibilityScore(shortageItems: any[], workstationCapacity: Record<string, WorkstationCapacity>) {
    let score = 100;
    
    // 物料短缺扣分
    score -= shortageItems.length * 15;
    
    // 产能紧张扣分
    Object.values(workstationCapacity).forEach(capacity => {
      if (capacity.status === 'tight') score -= 10;
      if (capacity.status === 'overloaded') score -= 20;
    });

    return Math.max(0, score);
  }
}

export const productionReleaseService = new ProductionReleaseService();
